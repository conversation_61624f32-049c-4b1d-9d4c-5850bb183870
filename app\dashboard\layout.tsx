import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Sidebar } from '@/components/sidebar/sidebar'
import { UserPresenceProvider } from '@/components/providers/user-presence-provider'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = await createClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return redirect('/auth/login')
  }

  // Fetch user's servers
  const { data: servers } = await supabase
    .from('servers')
    .select(`
      *,
      channels(*, server_id),
      server_members!inner(role)
    `)
    .eq('server_members.user_id', user.id)
    .order('created_at', { ascending: true })

  return (
    <UserPresenceProvider userId={user.id}>
      <div className="flex h-screen bg-background">
        {/* Sidebar */}
        <div className="w-72 glass-sidebar flex-shrink-0">
          <Sidebar servers={servers || []} currentUserId={user.id} />
        </div>
        
        {/* Main content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {children}
        </div>
      </div>
    </UserPresenceProvider>
  )
}
