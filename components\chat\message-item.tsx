'use client'

import { useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { MoreHorizontal, <PERSON>ly, Smile, Edit, Trash2, Pin } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import type { MessageWithAuthor } from '@/lib/supabase/types'

interface MessageItemProps {
  message: MessageWithAuthor
  isOwn: boolean
  isGrouped: boolean
  onReply: () => void
}

export function MessageItem({ message, isOwn, isGrouped, onReply }: MessageItemProps) {
  const [isHovered, setIsHovered] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500'
      case 'away':
        return 'bg-yellow-500'
      case 'busy':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  return (
    <div
      className={cn(
        "group relative px-4 py-1 hover:bg-muted/30 transition-colors",
        isGrouped ? "py-0.5" : "py-2"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex space-x-3">
        {/* Avatar (only show if not grouped) */}
        <div className="w-10 flex-shrink-0">
          {!isGrouped && (
            <div className="relative">
              <Avatar className="w-10 h-10">
                <AvatarImage src={message.author.avatar_url || undefined} />
                <AvatarFallback className="text-sm">
                  {message.author.display_name?.charAt(0) || message.author.username.charAt(0)}
                </AvatarFallback>
              </Avatar>
              
              {/* Status indicator */}
              <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${getStatusColor(message.author.status)}`} />
            </div>
          )}
        </div>

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          {/* Header (only show if not grouped) */}
          {!isGrouped && (
            <div className="flex items-baseline space-x-2 mb-1">
              <span className="font-semibold text-foreground hover:underline cursor-pointer">
                {message.author.display_name || message.author.username}
              </span>
              
              {/* User badges could go here */}
              
              <span className="text-xs text-muted-foreground">
                {formatTime(message.created_at)}
              </span>
              
              {message.edited_at && (
                <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
                  edited
                </Badge>
              )}
            </div>
          )}

          {/* Reply reference */}
          {message.reply_to_message && (
            <div className="flex items-center space-x-2 mb-2 text-sm text-muted-foreground">
              <Reply className="w-3 h-3" />
              <span>
                Replying to <span className="font-medium">{message.reply_to_message.author.username}</span>
              </span>
            </div>
          )}

          {/* Message text */}
          <div className={cn(
            "text-foreground break-words",
            isGrouped && "text-sm"
          )}>
            {message.content}
          </div>

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.attachments.map((attachment) => (
                <div key={attachment.id} className="glass-card p-3 rounded-lg max-w-md">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{attachment.filename}</p>
                      {attachment.file_size && (
                        <p className="text-xs text-muted-foreground">
                          {(attachment.file_size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      )}
                    </div>
                    <Button variant="outline" size="sm">
                      Download
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {/* Group reactions by emoji */}
              {Object.entries(
                message.reactions.reduce((acc, reaction) => {
                  if (!acc[reaction.emoji]) {
                    acc[reaction.emoji] = []
                  }
                  acc[reaction.emoji].push(reaction)
                  return acc
                }, {} as Record<string, typeof message.reactions>)
              ).map(([emoji, reactions]) => (
                <Button
                  key={emoji}
                  variant="outline"
                  size="sm"
                  className="h-6 px-2 text-xs hover:bg-primary/20"
                >
                  <span className="mr-1">{emoji}</span>
                  <span>{reactions.length}</span>
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* Message Actions (show on hover) */}
        {(isHovered || isOwn) && (
          <div className="absolute top-0 right-4 bg-background border border-border rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex items-center">
              <Button variant="ghost" size="icon" className="w-8 h-8" onClick={onReply}>
                <Reply className="w-4 h-4" />
              </Button>
              
              <Button variant="ghost" size="icon" className="w-8 h-8">
                <Smile className="w-4 h-4" />
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="w-8 h-8">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="glass-card border-border/50">
                  <DropdownMenuItem>
                    <Pin className="w-4 h-4 mr-2" />
                    Pin Message
                  </DropdownMenuItem>
                  
                  {isOwn && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Message
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem className="text-destructive">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Message
                      </DropdownMenuItem>
                    </>
                  )}
                  
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    Copy Message Link
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
