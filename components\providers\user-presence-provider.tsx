'use client'

import { createContext, useContext, useEffect, useRef } from 'react'
import { useUserPresence } from '@/lib/hooks/useRealtime'
import { realtimeManager } from '@/lib/supabase/realtime'

interface UserPresenceContextType {
  updatePresence: (status: 'online' | 'away' | 'busy' | 'offline') => Promise<void>
  getUserPresence: (userId: string) => any
  presenceMap: Map<string, any>
}

const UserPresenceContext = createContext<UserPresenceContextType | null>(null)

export function useUserPresenceContext() {
  const context = useContext(UserPresenceContext)
  if (!context) {
    throw new Error('useUserPresenceContext must be used within UserPresenceProvider')
  }
  return context
}

interface UserPresenceProviderProps {
  userId: string
  children: React.ReactNode
}

export function UserPresenceProvider({ userId, children }: UserPresenceProviderProps) {
  const { presenceMap, getUserPresence, updatePresence } = useUserPresence()
  const lastActivityRef = useRef<number>(Date.now())
  const awayTimeoutRef = useRef<NodeJS.Timeout>()
  const isAwayRef = useRef(false)

  // Set user as online when component mounts
  useEffect(() => {
    updatePresence('online')

    // Set user as offline when page unloads
    const handleBeforeUnload = () => {
      // Use sendBeacon for reliable offline status update
      navigator.sendBeacon('/api/presence/offline', JSON.stringify({ userId }))
    }

    const handleVisibilityChange = () => {
      if (document.hidden) {
        updatePresence('away')
      } else {
        updatePresence('online')
        lastActivityRef.current = Date.now()
        isAwayRef.current = false
      }
    }

    // Track user activity
    const handleActivity = () => {
      lastActivityRef.current = Date.now()
      
      if (isAwayRef.current) {
        updatePresence('online')
        isAwayRef.current = false
      }

      // Clear existing timeout
      if (awayTimeoutRef.current) {
        clearTimeout(awayTimeoutRef.current)
      }

      // Set user as away after 5 minutes of inactivity
      awayTimeoutRef.current = setTimeout(() => {
        updatePresence('away')
        isAwayRef.current = true
      }, 5 * 60 * 1000) // 5 minutes
    }

    // Activity event listeners
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true })
    })

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Initial activity setup
    handleActivity()

    return () => {
      // Cleanup
      events.forEach(event => {
        document.removeEventListener(event, handleActivity)
      })
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      
      if (awayTimeoutRef.current) {
        clearTimeout(awayTimeoutRef.current)
      }

      // Set user as offline
      updatePresence('offline')
    }
  }, [userId, updatePresence])

  // Periodic cleanup of typing indicators
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      realtimeManager.cleanupTypingIndicators()
    }, 30000) // Every 30 seconds

    return () => clearInterval(cleanupInterval)
  }, [])

  const contextValue: UserPresenceContextType = {
    updatePresence,
    getUserPresence,
    presenceMap
  }

  return (
    <UserPresenceContext.Provider value={contextValue}>
      {children}
    </UserPresenceContext.Provider>
  )
}
