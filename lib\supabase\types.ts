export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      attachments: {
        Row: {
          content_type: string | null
          created_at: string
          file_size: number | null
          file_url: string
          filename: string
          id: string
          message_id: string
        }
        Insert: {
          content_type?: string | null
          created_at?: string
          file_size?: number | null
          file_url: string
          filename: string
          id?: string
          message_id: string
        }
        Update: {
          content_type?: string | null
          created_at?: string
          file_size?: number | null
          file_url?: string
          filename?: string
          id?: string
          message_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "attachments_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          }
        ]
      }
      channel_permissions: {
        Row: {
          can_manage: boolean
          can_read: boolean
          can_write: boolean
          channel_id: string
          created_at: string
          id: string
          role: Database["public"]["Enums"]["server_role"] | null
          user_id: string | null
        }
        Insert: {
          can_manage?: boolean
          can_read?: boolean
          can_write?: boolean
          channel_id: string
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["server_role"] | null
          user_id?: string | null
        }
        Update: {
          can_manage?: boolean
          can_read?: boolean
          can_write?: boolean
          channel_id?: string
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["server_role"] | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "channel_permissions_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "channel_permissions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      channels: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_private: boolean
          name: string
          parent_id: string | null
          position: number
          server_id: string
          type: Database["public"]["Enums"]["channel_type"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean
          name: string
          parent_id?: string | null
          position?: number
          server_id: string
          type?: Database["public"]["Enums"]["channel_type"]
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean
          name?: string
          parent_id?: string | null
          position?: number
          server_id?: string
          type?: Database["public"]["Enums"]["channel_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "channels_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "channels_server_id_fkey"
            columns: ["server_id"]
            isOneToOne: false
            referencedRelation: "servers"
            referencedColumns: ["id"]
          }
        ]
      }
      messages: {
        Row: {
          author_id: string
          channel_id: string
          content: string | null
          created_at: string
          edited_at: string | null
          id: string
          reply_to: string | null
          type: Database["public"]["Enums"]["message_type"]
        }
        Insert: {
          author_id: string
          channel_id: string
          content?: string | null
          created_at?: string
          edited_at?: string | null
          id?: string
          reply_to?: string | null
          type?: Database["public"]["Enums"]["message_type"]
        }
        Update: {
          author_id?: string
          channel_id?: string
          content?: string | null
          created_at?: string
          edited_at?: string | null
          id?: string
          reply_to?: string | null
          type?: Database["public"]["Enums"]["message_type"]
        }
        Relationships: [
          {
            foreignKeyName: "messages_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_reply_to_fkey"
            columns: ["reply_to"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          display_name: string | null
          id: string
          last_seen: string
          status: Database["public"]["Enums"]["user_status"]
          status_text: string | null
          updated_at: string
          username: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          display_name?: string | null
          id: string
          last_seen?: string
          status?: Database["public"]["Enums"]["user_status"]
          status_text?: string | null
          updated_at?: string
          username: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          display_name?: string | null
          id?: string
          last_seen?: string
          status?: Database["public"]["Enums"]["user_status"]
          status_text?: string | null
          updated_at?: string
          username?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      reactions: {
        Row: {
          created_at: string
          emoji: string
          id: string
          message_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          emoji: string
          id?: string
          message_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          emoji?: string
          id?: string
          message_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reactions_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      server_invites: {
        Row: {
          code: string
          created_at: string
          expires_at: string | null
          id: string
          inviter_id: string
          max_uses: number | null
          server_id: string
          uses: number
        }
        Insert: {
          code?: string
          created_at?: string
          expires_at?: string | null
          id?: string
          inviter_id: string
          max_uses?: number | null
          server_id: string
          uses?: number
        }
        Update: {
          code?: string
          created_at?: string
          expires_at?: string | null
          id?: string
          inviter_id?: string
          max_uses?: number | null
          server_id?: string
          uses?: number
        }
        Relationships: [
          {
            foreignKeyName: "server_invites_inviter_id_fkey"
            columns: ["inviter_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "server_invites_server_id_fkey"
            columns: ["server_id"]
            isOneToOne: false
            referencedRelation: "servers"
            referencedColumns: ["id"]
          }
        ]
      }
      server_members: {
        Row: {
          id: string
          joined_at: string
          nickname: string | null
          role: Database["public"]["Enums"]["server_role"]
          server_id: string
          user_id: string
        }
        Insert: {
          id?: string
          joined_at?: string
          nickname?: string | null
          role?: Database["public"]["Enums"]["server_role"]
          server_id: string
          user_id: string
        }
        Update: {
          id?: string
          joined_at?: string
          nickname?: string | null
          role?: Database["public"]["Enums"]["server_role"]
          server_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "server_members_server_id_fkey"
            columns: ["server_id"]
            isOneToOne: false
            referencedRelation: "servers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "server_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      servers: {
        Row: {
          banner_url: string | null
          created_at: string
          description: string | null
          icon_url: string | null
          id: string
          invite_code: string
          name: string
          owner_id: string
          updated_at: string
        }
        Insert: {
          banner_url?: string | null
          created_at?: string
          description?: string | null
          icon_url?: string | null
          id?: string
          invite_code?: string
          name: string
          owner_id: string
          updated_at?: string
        }
        Update: {
          banner_url?: string | null
          created_at?: string
          description?: string | null
          icon_url?: string | null
          id?: string
          invite_code?: string
          name?: string
          owner_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "servers_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      typing_indicators: {
        Row: {
          channel_id: string
          id: string
          started_at: string
          user_id: string
        }
        Insert: {
          channel_id: string
          id?: string
          started_at?: string
          user_id: string
        }
        Update: {
          channel_id?: string
          id?: string
          started_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "typing_indicators_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "typing_indicators_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      user_presence: {
        Row: {
          last_seen: string
          status: Database["public"]["Enums"]["user_status"]
          updated_at: string
          user_id: string
        }
        Insert: {
          last_seen?: string
          status?: Database["public"]["Enums"]["user_status"]
          updated_at?: string
          user_id: string
        }
        Update: {
          last_seen?: string
          status?: Database["public"]["Enums"]["user_status"]
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_presence_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      cleanup_typing_indicators: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_user_presence: {
        Args: {
          user_uuid: string
          new_status: Database["public"]["Enums"]["user_status"]
        }
        Returns: undefined
      }
    }
    Enums: {
      channel_type: "text" | "voice" | "category"
      message_type: "text" | "system" | "join" | "leave"
      server_role: "owner" | "admin" | "moderator" | "member"
      user_status: "online" | "away" | "busy" | "offline"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Convenience types
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Server = Database['public']['Tables']['servers']['Row']
export type Channel = Database['public']['Tables']['channels']['Row']
export type Message = Database['public']['Tables']['messages']['Row']
export type ServerMember = Database['public']['Tables']['server_members']['Row']
export type Attachment = Database['public']['Tables']['attachments']['Row']
export type Reaction = Database['public']['Tables']['reactions']['Row']
export type UserPresence = Database['public']['Tables']['user_presence']['Row']
export type TypingIndicator = Database['public']['Tables']['typing_indicators']['Row']

export type UserStatus = Database['public']['Enums']['user_status']
export type ChannelType = Database['public']['Enums']['channel_type']
export type ServerRole = Database['public']['Enums']['server_role']
export type MessageType = Database['public']['Enums']['message_type']

// Extended types with relations
export type MessageWithAuthor = Message & {
  author: Profile
  attachments?: Attachment[]
  reactions?: (Reaction & { user: Profile })[]
  reply_to_message?: MessageWithAuthor
}

export type ChannelWithMessages = Channel & {
  messages?: MessageWithAuthor[]
}

export type ServerWithChannels = Server & {
  channels?: Channel[]
  members?: (ServerMember & { user: Profile })[]
  owner: Profile
}

export type ProfileWithPresence = Profile & {
  presence?: UserPresence
}
