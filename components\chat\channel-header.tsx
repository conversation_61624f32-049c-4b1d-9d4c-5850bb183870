'use client'

import { <PERSON>h, Volume2, <PERSON>, <PERSON>, Pin, <PERSON>, <PERSON><PERSON><PERSON>, MoreHorizontal } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import type { Channel, Server } from '@/lib/supabase/types'

interface ChannelHeaderProps {
  channel: Channel & { server: Server }
  server: Server
  currentUserId: string
}

export function ChannelHeader({ channel, server, currentUserId }: ChannelHeaderProps) {
  const getChannelIcon = () => {
    switch (channel.type) {
      case 'voice':
        return <Volume2 className="w-5 h-5 text-muted-foreground" />
      case 'category':
        return null
      default:
        return <Hash className="w-5 h-5 text-muted-foreground" />
    }
  }

  const isOwner = server.owner_id === currentUserId

  return (
    <div className="h-12 border-b border-border/50 flex items-center justify-between px-4 bg-background/80 backdrop-blur-sm">
      {/* Left side - Channel info */}
      <div className="flex items-center space-x-3 min-w-0 flex-1">
        {getChannelIcon()}
        
        <div className="min-w-0 flex-1">
          <div className="flex items-center space-x-2">
            <h1 className="font-semibold text-foreground truncate">
              {channel.name}
            </h1>
            
            {channel.is_private && (
              <Badge variant="secondary" className="text-xs">
                Private
              </Badge>
            )}
          </div>
          
          {channel.description && (
            <p className="text-sm text-muted-foreground truncate">
              {channel.description}
            </p>
          )}
        </div>
      </div>

      {/* Center - Search */}
      <div className="hidden md:flex items-center mx-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search messages..."
            className="w-64 pl-10 bg-muted/50 border-border/50 focus:bg-background"
          />
        </div>
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center space-x-1">
        {/* Notification Bell */}
        <Button variant="ghost" size="icon" className="w-8 h-8">
          <Bell className="w-4 h-4" />
        </Button>

        {/* Pinned Messages */}
        <Button variant="ghost" size="icon" className="w-8 h-8">
          <Pin className="w-4 h-4" />
        </Button>

        {/* Member List */}
        <Button variant="ghost" size="icon" className="w-8 h-8">
          <Users className="w-4 h-4" />
        </Button>

        {/* Search (mobile) */}
        <Button variant="ghost" size="icon" className="w-8 h-8 md:hidden">
          <Search className="w-4 h-4" />
        </Button>

        {/* Channel Settings */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="w-8 h-8">
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="glass-card border-border/50">
            <DropdownMenuItem>
              <Pin className="w-4 h-4 mr-2" />
              View Pinned Messages
            </DropdownMenuItem>
            
            <DropdownMenuItem>
              <Bell className="w-4 h-4 mr-2" />
              Notification Settings
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            {isOwner && (
              <>
                <DropdownMenuItem>
                  <Settings className="w-4 h-4 mr-2" />
                  Edit Channel
                </DropdownMenuItem>
                
                <DropdownMenuItem className="text-destructive">
                  Delete Channel
                </DropdownMenuItem>
              </>
            )}
            
            <DropdownMenuItem>
              Copy Channel Link
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
