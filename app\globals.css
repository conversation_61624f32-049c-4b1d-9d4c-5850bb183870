@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--background) / 0.95) 50%,
      hsl(var(--background) / 0.9) 100%
    );
    min-height: 100vh;
  }
}

@layer components {
  /* Glassmorphism effects */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .glass-dark {
    @apply backdrop-blur-md bg-black/10 border border-white/10;
  }

  .glass-card {
    @apply backdrop-blur-lg bg-white/5 border border-white/10 rounded-xl shadow-xl;
  }

  .glass-sidebar {
    @apply backdrop-blur-xl bg-background/80 border-r border-border/50;
  }

  /* Modern gradients */
  .gradient-primary {
    background: linear-gradient(135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--primary) / 0.8) 100%
    );
  }

  .gradient-secondary {
    background: linear-gradient(135deg,
      hsl(var(--secondary)) 0%,
      hsl(var(--secondary) / 0.8) 100%
    );
  }

  .gradient-accent {
    background: linear-gradient(135deg,
      hsl(var(--accent)) 0%,
      hsl(var(--accent) / 0.8) 100%
    );
  }

  /* Chat message styles */
  .message-bubble {
    @apply glass-card p-3 max-w-[70%] break-words;
  }

  .message-bubble-own {
    @apply gradient-primary text-primary-foreground ml-auto;
  }

  .message-bubble-other {
    @apply glass-card mr-auto;
  }

  /* Hover effects */
  .hover-glass {
    @apply transition-all duration-200 hover:bg-white/20 hover:border-white/30;
  }

  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg;
  }

  /* Scrollbar styling */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground));
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-subtle {
    animation: pulseSubtle 2s infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulseSubtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
