-- Clear Schema SQL - Removes everything created by schema.sql
-- WARNING: This will delete ALL data and structures created by the Stack application
-- Run this ONLY if you want to completely reset the database

-- ============================================================================
-- STEP 1: Remove Realtime Publications
-- ============================================================================

-- Remove tables from realtime publication
ALTER PUBLICATION supabase_realtime DROP TABLE messages;
ALTER PUBLICATION supabase_realtime DROP TABLE user_presence;
ALTER PUBLICATION supabase_realtime DROP TABLE typing_indicators;
ALTER PUBLICATION supabase_realtime DROP TABLE reactions;
ALTER PUBLICATION supabase_realtime DROP TABLE server_members;

-- ============================================================================
-- STEP 2: Drop Triggers (must be done before dropping functions)
-- ============================================================================

DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
DROP TRIGGER IF EXISTS update_servers_updated_at ON servers;
DROP TRIGGER IF EXISTS update_channels_updated_at ON channels;

-- ============================================================================
-- STEP 3: Drop Tables (in reverse dependency order)
-- ============================================================================

-- Drop tables that reference other tables first
DROP TABLE IF EXISTS server_invites CASCADE;
DROP TABLE IF EXISTS channel_permissions CASCADE;
DROP TABLE IF EXISTS typing_indicators CASCADE;
DROP TABLE IF EXISTS user_presence CASCADE;
DROP TABLE IF EXISTS reactions CASCADE;
DROP TABLE IF EXISTS attachments CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS channels CASCADE;
DROP TABLE IF EXISTS server_members CASCADE;
DROP TABLE IF EXISTS servers CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- ============================================================================
-- STEP 4: Drop Functions
-- ============================================================================

DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS cleanup_typing_indicators() CASCADE;
DROP FUNCTION IF EXISTS update_user_presence(UUID, user_status) CASCADE;

-- ============================================================================
-- STEP 5: Drop Custom Types
-- ============================================================================

DROP TYPE IF EXISTS user_status CASCADE;
DROP TYPE IF EXISTS channel_type CASCADE;
DROP TYPE IF EXISTS server_role CASCADE;
DROP TYPE IF EXISTS message_type CASCADE;

-- ============================================================================
-- STEP 6: Drop Extensions (Optional - only if you want to remove them completely)
-- ============================================================================

-- Uncomment these lines if you want to remove the extensions entirely
-- Note: This might affect other applications using the same database
-- DROP EXTENSION IF EXISTS "uuid-ossp";
-- DROP EXTENSION IF EXISTS "pgcrypto";

-- Schema cleanup completed
