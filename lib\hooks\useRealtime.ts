'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { realtimeManager } from '@/lib/supabase/realtime'
import type { 
  Message, 
  MessageWithAuthor, 
  UserPresence, 
  TypingIndicator,
  Reaction,
  Profile
} from '@/lib/supabase/types'

// Hook for real-time messages in a channel
export function useChannelMessages(channelId: string | null) {
  const [messages, setMessages] = useState<MessageWithAuthor[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  // Fetch initial messages
  const fetchMessages = useCallback(async () => {
    if (!channelId) return

    try {
      setLoading(true)
      setError(null)

      const { data, error: fetchError } = await supabase
        .from('messages')
        .select(`
          *,
          author:profiles(*),
          attachments(*),
          reactions(*, user:profiles(*)),
          reply_to_message:messages(*, author:profiles(*))
        `)
        .eq('channel_id', channelId)
        .order('created_at', { ascending: true })
        .limit(50)

      if (fetchError) throw fetchError

      setMessages(data as MessageWithAuthor[] || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch messages')
    } finally {
      setLoading(false)
    }
  }, [channelId, supabase])

  // Handle real-time message updates
  useEffect(() => {
    if (!channelId) return

    fetchMessages()

    const subscriptionKey = realtimeManager.subscribeToMessages(
      channelId,
      async (event) => {
        if (event.eventType === 'INSERT') {
          // Fetch the complete message with relations
          const { data: newMessage } = await supabase
            .from('messages')
            .select(`
              *,
              author:profiles(*),
              attachments(*),
              reactions(*, user:profiles(*)),
              reply_to_message:messages(*, author:profiles(*))
            `)
            .eq('id', event.new.id)
            .single()

          if (newMessage) {
            setMessages(prev => [...prev, newMessage as MessageWithAuthor])
          }
        } else if (event.eventType === 'UPDATE') {
          setMessages(prev => 
            prev.map(msg => 
              msg.id === event.new.id 
                ? { ...msg, ...event.new }
                : msg
            )
          )
        } else if (event.eventType === 'DELETE') {
          setMessages(prev => prev.filter(msg => msg.id !== event.old.id))
        }
      },
      (error) => setError('Real-time connection error')
    )

    return () => {
      realtimeManager.unsubscribe(subscriptionKey)
    }
  }, [channelId, fetchMessages, supabase])

  // Send a new message
  const sendMessage = useCallback(async (content: string, replyTo?: string) => {
    if (!channelId) return

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      const { error } = await supabase
        .from('messages')
        .insert({
          channel_id: channelId,
          author_id: user.id,
          content,
          reply_to: replyTo || null
        })

      if (error) throw error
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message')
    }
  }, [channelId, supabase])

  return {
    messages,
    loading,
    error,
    sendMessage,
    refetch: fetchMessages
  }
}

// Hook for user presence tracking
export function useUserPresence() {
  const [presenceMap, setPresenceMap] = useState<Map<string, UserPresence>>(new Map())
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  // Fetch initial presence data
  useEffect(() => {
    const fetchPresence = async () => {
      try {
        const { data, error } = await supabase
          .from('user_presence')
          .select('*')

        if (error) throw error

        const map = new Map<string, UserPresence>()
        data?.forEach(presence => {
          map.set(presence.user_id, presence)
        })
        setPresenceMap(map)
      } catch (err) {
        console.error('Error fetching presence:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPresence()

    // Subscribe to presence updates
    const subscriptionKey = realtimeManager.subscribeToPresence(
      (event) => {
        setPresenceMap(prev => {
          const newMap = new Map(prev)
          if (event.eventType === 'INSERT' || event.eventType === 'UPDATE') {
            newMap.set(event.new.user_id, event.new)
          } else if (event.eventType === 'DELETE') {
            newMap.delete(event.old.user_id)
          }
          return newMap
        })
      }
    )

    return () => {
      realtimeManager.unsubscribe(subscriptionKey)
    }
  }, [supabase])

  const getUserPresence = useCallback((userId: string) => {
    return presenceMap.get(userId)
  }, [presenceMap])

  const updatePresence = useCallback(async (status: 'online' | 'away' | 'busy' | 'offline') => {
    const { data: { user } } = await supabase.auth.getUser()
    if (user) {
      await realtimeManager.updatePresence(user.id, status)
    }
  }, [supabase])

  return {
    presenceMap,
    loading,
    getUserPresence,
    updatePresence
  }
}

// Hook for typing indicators
export function useTypingIndicators(channelId: string | null) {
  const [typingUsers, setTypingUsers] = useState<Map<string, Profile>>(new Map())
  const typingTimeoutRef = useRef<NodeJS.Timeout>()
  const supabase = createClient()

  useEffect(() => {
    if (!channelId) return

    // Subscribe to typing indicators
    const subscriptionKey = realtimeManager.subscribeToTyping(
      channelId,
      async (event) => {
        if (event.eventType === 'INSERT' || event.eventType === 'UPDATE') {
          // Fetch user profile for typing indicator
          const { data: profile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', event.new.user_id)
            .single()

          if (profile) {
            setTypingUsers(prev => {
              const newMap = new Map(prev)
              newMap.set(event.new.user_id, profile)
              return newMap
            })

            // Auto-remove typing indicator after 10 seconds
            setTimeout(() => {
              setTypingUsers(prev => {
                const newMap = new Map(prev)
                newMap.delete(event.new.user_id)
                return newMap
              })
            }, 10000)
          }
        } else if (event.eventType === 'DELETE') {
          setTypingUsers(prev => {
            const newMap = new Map(prev)
            newMap.delete(event.old.user_id)
            return newMap
          })
        }
      }
    )

    return () => {
      realtimeManager.unsubscribe(subscriptionKey)
    }
  }, [channelId, supabase])

  const startTyping = useCallback(async () => {
    if (!channelId) return

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    await realtimeManager.startTyping(channelId, user.id)

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Auto-stop typing after 5 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping()
    }, 5000)
  }, [channelId, supabase])

  const stopTyping = useCallback(async () => {
    if (!channelId) return

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    await realtimeManager.stopTyping(channelId, user.id)

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }
  }, [channelId, supabase])

  return {
    typingUsers: Array.from(typingUsers.values()),
    startTyping,
    stopTyping
  }
}

// Hook for message reactions
export function useMessageReactions(messageIds: string[]) {
  const [reactions, setReactions] = useState<Map<string, Reaction[]>>(new Map())
  const supabase = createClient()

  useEffect(() => {
    if (messageIds.length === 0) return

    // Fetch initial reactions
    const fetchReactions = async () => {
      const { data } = await supabase
        .from('reactions')
        .select('*, user:profiles(*)')
        .in('message_id', messageIds)

      if (data) {
        const reactionMap = new Map<string, Reaction[]>()
        data.forEach(reaction => {
          const messageReactions = reactionMap.get(reaction.message_id) || []
          messageReactions.push(reaction)
          reactionMap.set(reaction.message_id, messageReactions)
        })
        setReactions(reactionMap)
      }
    }

    fetchReactions()

    // Subscribe to reaction updates
    const subscriptionKey = realtimeManager.subscribeToReactions(
      messageIds,
      (event) => {
        setReactions(prev => {
          const newMap = new Map(prev)
          const messageId = event.new?.message_id || event.old?.message_id

          if (event.eventType === 'INSERT') {
            const messageReactions = newMap.get(messageId) || []
            messageReactions.push(event.new)
            newMap.set(messageId, messageReactions)
          } else if (event.eventType === 'DELETE') {
            const messageReactions = newMap.get(messageId) || []
            const filtered = messageReactions.filter(r => r.id !== event.old.id)
            newMap.set(messageId, filtered)
          }

          return newMap
        })
      }
    )

    return () => {
      realtimeManager.unsubscribe(subscriptionKey)
    }
  }, [messageIds, supabase])

  const addReaction = useCallback(async (messageId: string, emoji: string) => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    try {
      await supabase
        .from('reactions')
        .insert({
          message_id: messageId,
          user_id: user.id,
          emoji
        })
    } catch (error) {
      console.error('Error adding reaction:', error)
    }
  }, [supabase])

  const removeReaction = useCallback(async (messageId: string, emoji: string) => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    try {
      await supabase
        .from('reactions')
        .delete()
        .match({
          message_id: messageId,
          user_id: user.id,
          emoji
        })
    } catch (error) {
      console.error('Error removing reaction:', error)
    }
  }, [supabase])

  return {
    reactions,
    addReaction,
    removeReaction
  }
}
