'use client'

import { MessageItem } from './message-item'
import type { MessageWithAuthor } from '@/lib/supabase/types'

interface MessageListProps {
  messages: MessageWithAuthor[]
  currentUserId: string
  onReply: (messageId: string) => void
}

export function MessageList({ messages, currentUserId, onReply }: MessageListProps) {
  // Group messages by date
  const groupedMessages = messages.reduce((groups, message) => {
    const date = new Date(message.created_at).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
    return groups
  }, {} as Record<string, MessageWithAuthor[]>)

  // Check if messages should be grouped (same author, within 5 minutes)
  const shouldGroupWithPrevious = (current: MessageWithAuthor, previous: MessageWithAuthor) => {
    if (current.author_id !== previous.author_id) return false
    
    const currentTime = new Date(current.created_at).getTime()
    const previousTime = new Date(previous.created_at).getTime()
    const timeDiff = currentTime - previousTime
    
    return timeDiff < 5 * 60 * 1000 // 5 minutes
  }

  return (
    <div className="space-y-6">
      {Object.entries(groupedMessages).map(([date, dayMessages]) => (
        <div key={date}>
          {/* Date Separator */}
          <div className="flex items-center justify-center py-2">
            <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
              {new Date(date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
          </div>

          {/* Messages for this date */}
          <div className="space-y-1">
            {dayMessages.map((message, index) => {
              const previousMessage = index > 0 ? dayMessages[index - 1] : null
              const isGrouped = previousMessage ? shouldGroupWithPrevious(message, previousMessage) : false
              
              return (
                <MessageItem
                  key={message.id}
                  message={message}
                  isOwn={message.author_id === currentUserId}
                  isGrouped={isGrouped}
                  onReply={() => onReply(message.id)}
                />
              )
            })}
          </div>
        </div>
      ))}
    </div>
  )
}
