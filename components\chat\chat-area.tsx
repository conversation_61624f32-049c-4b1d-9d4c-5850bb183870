'use client'

import { useEffect, useRef } from 'react'
import { MessageList } from './message-list'
import { MessageInput } from './message-input'
import { TypingIndicators } from './typing-indicators'
import { useChannelMessages, useTypingIndicators } from '@/lib/hooks/useRealtime'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader2 } from 'lucide-react'

interface ChatAreaProps {
  channelId: string
  currentUserId: string
  canWrite: boolean
}

export function ChatArea({ channelId, currentUserId, canWrite }: ChatAreaProps) {
  const { messages, loading, error, sendMessage } = useChannelMessages(channelId)
  const { typingUsers, startTyping, stopTyping } = useTypingIndicators(channelId)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = async (content: string, replyTo?: string) => {
    await sendMessage(content, replyTo)
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto text-muted-foreground" />
          <p className="text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center space-y-4">
          <p className="text-destructive">Failed to load messages</p>
          <p className="text-sm text-muted-foreground">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Messages Area */}
      <div className="flex-1 relative">
        <ScrollArea className="h-full custom-scrollbar">
          <div className="p-4 space-y-4">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">👋</div>
                <h3 className="text-xl font-semibold mb-2">Welcome to the beginning!</h3>
                <p className="text-muted-foreground">
                  This is the start of your conversation. Send a message to get things going!
                </p>
              </div>
            ) : (
              <MessageList 
                messages={messages} 
                currentUserId={currentUserId}
                onReply={(messageId) => {
                  // Handle reply functionality
                  console.log('Reply to:', messageId)
                }}
              />
            )}
            
            {/* Typing Indicators */}
            {typingUsers.length > 0 && (
              <TypingIndicators users={typingUsers} />
            )}
            
            {/* Auto-scroll anchor */}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </div>

      {/* Message Input */}
      {canWrite ? (
        <div className="border-t border-border/50 bg-background/80 backdrop-blur-sm">
          <MessageInput
            onSendMessage={handleSendMessage}
            onStartTyping={startTyping}
            onStopTyping={stopTyping}
            placeholder={`Message #${channelId.slice(0, 8)}...`}
          />
        </div>
      ) : (
        <div className="border-t border-border/50 bg-background/80 backdrop-blur-sm p-4">
          <div className="text-center text-muted-foreground">
            <p>You don't have permission to send messages in this channel.</p>
          </div>
        </div>
      )}
    </div>
  )
}
