import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Users, MessageSquare, Server } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default async function DashboardPage() {
  const supabase = await createClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return redirect('/auth/login')
  }

  // Fetch user's servers
  const { data: servers } = await supabase
    .from('servers')
    .select(`
      *,
      server_members!inner(role)
    `)
    .eq('server_members.user_id', user.id)
    .order('created_at', { ascending: true })

  // If user has servers, redirect to the first one
  if (servers && servers.length > 0) {
    const firstServer = servers[0]
    
    // Get the first channel in the server
    const { data: channels } = await supabase
      .from('channels')
      .select('id')
      .eq('server_id', firstServer.id)
      .eq('type', 'text')
      .order('position', { ascending: true })
      .limit(1)

    if (channels && channels.length > 0) {
      return redirect(`/servers/${firstServer.id}/channels/${channels[0].id}`)
    } else {
      return redirect(`/servers/${firstServer.id}`)
    }
  }

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="max-w-2xl w-full space-y-8">
        {/* Welcome Header */}
        <div className="text-center space-y-4">
          <div className="w-20 h-20 mx-auto rounded-full gradient-primary flex items-center justify-center">
            <MessageSquare className="w-10 h-10 text-white" />
          </div>
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Welcome to Stack
            </h1>
            <p className="text-xl text-muted-foreground mt-2">
              Your modern communication platform
            </p>
          </div>
        </div>

        {/* Getting Started Cards */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="glass-card hover-lift">
            <CardHeader>
              <div className="w-12 h-12 rounded-lg gradient-primary flex items-center justify-center mb-2">
                <Server className="w-6 h-6 text-white" />
              </div>
              <CardTitle>Create a Server</CardTitle>
              <CardDescription>
                Start your own community and invite friends to join
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Create Your Server
              </Button>
            </CardContent>
          </Card>

          <Card className="glass-card hover-lift">
            <CardHeader>
              <div className="w-12 h-12 rounded-lg gradient-secondary flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-white" />
              </div>
              <CardTitle>Join a Server</CardTitle>
              <CardDescription>
                Connect with existing communities using an invite link
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full">
                Join Server
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Features Overview */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-center">What you can do with Stack</h2>
          
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center space-y-2">
              <div className="w-16 h-16 mx-auto rounded-full bg-blue-500/20 flex items-center justify-center">
                <MessageSquare className="w-8 h-8 text-blue-500" />
              </div>
              <h3 className="font-medium">Real-time Chat</h3>
              <p className="text-sm text-muted-foreground">
                Instant messaging with typing indicators and reactions
              </p>
            </div>
            
            <div className="text-center space-y-2">
              <div className="w-16 h-16 mx-auto rounded-full bg-green-500/20 flex items-center justify-center">
                <Users className="w-8 h-8 text-green-500" />
              </div>
              <h3 className="font-medium">Community Building</h3>
              <p className="text-sm text-muted-foreground">
                Organize channels and manage member roles
              </p>
            </div>
            
            <div className="text-center space-y-2">
              <div className="w-16 h-16 mx-auto rounded-full bg-purple-500/20 flex items-center justify-center">
                <Server className="w-8 h-8 text-purple-500" />
              </div>
              <h3 className="font-medium">Multiple Servers</h3>
              <p className="text-sm text-muted-foreground">
                Join multiple communities and switch between them
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center space-y-4 pt-8">
          <p className="text-muted-foreground">
            Ready to get started? Create your first server or join an existing community.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" className="gradient-primary">
              Create Server
            </Button>
            <Button size="lg" variant="outline">
              Join with Invite
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
