import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const errorData = await request.json()
    
    // Log the error server-side with more context
    console.error('🚨 AUTH ERROR LOGGED:', {
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      referer: request.headers.get('referer'),
      ...errorData
    })

    // Optionally, you could store this in a database table for persistent logging
    // const supabase = await createClient()
    // await supabase.from('error_logs').insert({
    //   error_type: 'auth_signup',
    //   error_data: errorData,
    //   user_agent: request.headers.get('user-agent'),
    //   ip_address: request.headers.get('x-forwarded-for'),
    //   created_at: new Date().toISOString()
    // })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to log auth error:', error)
    return NextResponse.json({ error: 'Failed to log error' }, { status: 500 })
  }
}
