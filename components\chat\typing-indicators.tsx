'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import type { Profile } from '@/lib/supabase/types'

interface TypingIndicatorsProps {
  users: Profile[]
}

export function TypingIndicators({ users }: TypingIndicatorsProps) {
  if (users.length === 0) return null

  const getTypingText = () => {
    if (users.length === 1) {
      return `${users[0].display_name || users[0].username} is typing...`
    } else if (users.length === 2) {
      return `${users[0].display_name || users[0].username} and ${users[1].display_name || users[1].username} are typing...`
    } else {
      return `${users.length} people are typing...`
    }
  }

  return (
    <div className="flex items-center space-x-3 px-4 py-2 animate-fade-in">
      {/* User avatars */}
      <div className="flex -space-x-2">
        {users.slice(0, 3).map((user) => (
          <Avatar key={user.id} className="w-6 h-6 border-2 border-background">
            <AvatarImage src={user.avatar_url || undefined} />
            <AvatarFallback className="text-xs">
              {user.display_name?.charAt(0) || user.username.charAt(0)}
            </AvatarFallback>
          </Avatar>
        ))}
      </div>

      {/* Typing text with animated dots */}
      <div className="flex items-center space-x-1 text-sm text-muted-foreground">
        <span>{getTypingText()}</span>
        <div className="flex space-x-1">
          <div className="w-1 h-1 bg-muted-foreground rounded-full animate-pulse-subtle" style={{ animationDelay: '0ms' }} />
          <div className="w-1 h-1 bg-muted-foreground rounded-full animate-pulse-subtle" style={{ animationDelay: '200ms' }} />
          <div className="w-1 h-1 bg-muted-foreground rounded-full animate-pulse-subtle" style={{ animationDelay: '400ms' }} />
        </div>
      </div>
    </div>
  )
}
