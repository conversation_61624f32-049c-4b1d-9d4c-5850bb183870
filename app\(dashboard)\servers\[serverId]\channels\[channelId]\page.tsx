import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ChatArea } from '@/components/chat/chat-area'
import { ChannelHeader } from '@/components/chat/channel-header'

interface ChannelPageProps {
  params: {
    serverId: string
    channelId: string
  }
}

export default async function ChannelPage({ params }: ChannelPageProps) {
  const supabase = await createClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return redirect('/auth/login')
  }

  // Verify user has access to this channel
  const { data: channel } = await supabase
    .from('channels')
    .select(`
      *,
      server:servers(
        *,
        server_members!inner(role)
      )
    `)
    .eq('id', params.channelId)
    .eq('server_id', params.serverId)
    .eq('server.server_members.user_id', user.id)
    .single()

  if (!channel) {
    return redirect('/dashboard')
  }

  // Check if user has read access to this channel
  if (channel.is_private) {
    const { data: permission } = await supabase
      .from('channel_permissions')
      .select('can_read')
      .eq('channel_id', params.channelId)
      .eq('user_id', user.id)
      .single()

    if (!permission?.can_read) {
      return redirect(`/servers/${params.serverId}`)
    }
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Channel Header */}
      <ChannelHeader 
        channel={channel} 
        server={channel.server} 
        currentUserId={user.id}
      />
      
      {/* Chat Area */}
      <ChatArea 
        channelId={params.channelId}
        currentUserId={user.id}
        canWrite={!channel.is_private} // Simplified for now
      />
    </div>
  )
}
