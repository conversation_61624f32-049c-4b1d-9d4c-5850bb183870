import { createClient } from './client'
import type { 
  Message, 
  UserPresence, 
  TypingIndicator, 
  Reaction,
  ServerMember,
  MessageWithAuthor,
  Profile
} from './types'

export type RealtimeEvent<T = any> = {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE'
  new: T
  old: T
  schema: string
  table: string
}

export type MessageEvent = RealtimeEvent<Message>
export type PresenceEvent = RealtimeEvent<UserPresence>
export type TypingEvent = RealtimeEvent<TypingIndicator>
export type ReactionEvent = RealtimeEvent<Reaction>
export type MemberEvent = RealtimeEvent<ServerMember>

export class RealtimeManager {
  private supabase = createClient()
  private subscriptions = new Map<string, any>()

  // Subscribe to messages in a specific channel
  subscribeToMessages(
    channelId: string,
    onMessage: (event: MessageEvent) => void,
    onError?: (error: any) => void
  ) {
    const subscriptionKey = `messages_${channelId}`
    
    if (this.subscriptions.has(subscriptionKey)) {
      this.unsubscribe(subscriptionKey)
    }

    const subscription = this.supabase
      .channel(`messages:${channelId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'messages',
          filter: `channel_id=eq.${channelId}`
        },
        (payload) => {
          onMessage(payload as MessageEvent)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to messages in channel ${channelId}`)
        } else if (status === 'CHANNEL_ERROR' && onError) {
          onError(status)
        }
      })

    this.subscriptions.set(subscriptionKey, subscription)
    return subscriptionKey
  }

  // Subscribe to user presence updates
  subscribeToPresence(
    onPresenceUpdate: (event: PresenceEvent) => void,
    onError?: (error: any) => void
  ) {
    const subscriptionKey = 'user_presence'
    
    if (this.subscriptions.has(subscriptionKey)) {
      this.unsubscribe(subscriptionKey)
    }

    const subscription = this.supabase
      .channel('user_presence')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence'
        },
        (payload) => {
          onPresenceUpdate(payload as PresenceEvent)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to user presence updates')
        } else if (status === 'CHANNEL_ERROR' && onError) {
          onError(status)
        }
      })

    this.subscriptions.set(subscriptionKey, subscription)
    return subscriptionKey
  }

  // Subscribe to typing indicators in a specific channel
  subscribeToTyping(
    channelId: string,
    onTypingUpdate: (event: TypingEvent) => void,
    onError?: (error: any) => void
  ) {
    const subscriptionKey = `typing_${channelId}`
    
    if (this.subscriptions.has(subscriptionKey)) {
      this.unsubscribe(subscriptionKey)
    }

    const subscription = this.supabase
      .channel(`typing:${channelId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'typing_indicators',
          filter: `channel_id=eq.${channelId}`
        },
        (payload) => {
          onTypingUpdate(payload as TypingEvent)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to typing indicators in channel ${channelId}`)
        } else if (status === 'CHANNEL_ERROR' && onError) {
          onError(status)
        }
      })

    this.subscriptions.set(subscriptionKey, subscription)
    return subscriptionKey
  }

  // Subscribe to reactions on messages
  subscribeToReactions(
    messageIds: string[],
    onReactionUpdate: (event: ReactionEvent) => void,
    onError?: (error: any) => void
  ) {
    const subscriptionKey = `reactions_${messageIds.join('_')}`
    
    if (this.subscriptions.has(subscriptionKey)) {
      this.unsubscribe(subscriptionKey)
    }

    const subscription = this.supabase
      .channel(`reactions:${messageIds.join('_')}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'reactions',
          filter: `message_id=in.(${messageIds.join(',')})`
        },
        (payload) => {
          onReactionUpdate(payload as ReactionEvent)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to reactions for messages: ${messageIds.join(', ')}`)
        } else if (status === 'CHANNEL_ERROR' && onError) {
          onError(status)
        }
      })

    this.subscriptions.set(subscriptionKey, subscription)
    return subscriptionKey
  }

  // Subscribe to server member updates
  subscribeToServerMembers(
    serverId: string,
    onMemberUpdate: (event: MemberEvent) => void,
    onError?: (error: any) => void
  ) {
    const subscriptionKey = `members_${serverId}`
    
    if (this.subscriptions.has(subscriptionKey)) {
      this.unsubscribe(subscriptionKey)
    }

    const subscription = this.supabase
      .channel(`members:${serverId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'server_members',
          filter: `server_id=eq.${serverId}`
        },
        (payload) => {
          onMemberUpdate(payload as MemberEvent)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to server members for server ${serverId}`)
        } else if (status === 'CHANNEL_ERROR' && onError) {
          onError(status)
        }
      })

    this.subscriptions.set(subscriptionKey, subscription)
    return subscriptionKey
  }

  // Unsubscribe from a specific subscription
  unsubscribe(subscriptionKey: string) {
    const subscription = this.subscriptions.get(subscriptionKey)
    if (subscription) {
      this.supabase.removeChannel(subscription)
      this.subscriptions.delete(subscriptionKey)
      console.log(`Unsubscribed from ${subscriptionKey}`)
    }
  }

  // Unsubscribe from all subscriptions
  unsubscribeAll() {
    for (const [key, subscription] of this.subscriptions) {
      this.supabase.removeChannel(subscription)
      console.log(`Unsubscribed from ${key}`)
    }
    this.subscriptions.clear()
  }

  // Send typing indicator
  async startTyping(channelId: string, userId: string) {
    try {
      await this.supabase
        .from('typing_indicators')
        .upsert({
          channel_id: channelId,
          user_id: userId,
          started_at: new Date().toISOString()
        })
    } catch (error) {
      console.error('Error starting typing indicator:', error)
    }
  }

  // Stop typing indicator
  async stopTyping(channelId: string, userId: string) {
    try {
      await this.supabase
        .from('typing_indicators')
        .delete()
        .match({ channel_id: channelId, user_id: userId })
    } catch (error) {
      console.error('Error stopping typing indicator:', error)
    }
  }

  // Update user presence
  async updatePresence(userId: string, status: 'online' | 'away' | 'busy' | 'offline') {
    try {
      await this.supabase.rpc('update_user_presence', {
        user_uuid: userId,
        new_status: status
      })
    } catch (error) {
      console.error('Error updating presence:', error)
    }
  }

  // Clean up old typing indicators
  async cleanupTypingIndicators() {
    try {
      await this.supabase.rpc('cleanup_typing_indicators')
    } catch (error) {
      console.error('Error cleaning up typing indicators:', error)
    }
  }
}

// Singleton instance
export const realtimeManager = new RealtimeManager()

// Utility functions for common operations
export const subscribeToChannelMessages = (
  channelId: string,
  onMessage: (event: MessageEvent) => void
) => {
  return realtimeManager.subscribeToMessages(channelId, onMessage)
}

export const subscribeToUserPresence = (
  onPresenceUpdate: (event: PresenceEvent) => void
) => {
  return realtimeManager.subscribeToPresence(onPresenceUpdate)
}

export const startTyping = (channelId: string, userId: string) => {
  return realtimeManager.startTyping(channelId, userId)
}

export const stopTyping = (channelId: string, userId: string) => {
  return realtimeManager.stopTyping(channelId, userId)
}

export const updateUserPresence = (userId: string, status: 'online' | 'away' | 'busy' | 'offline') => {
  return realtimeManager.updatePresence(userId, status)
}
