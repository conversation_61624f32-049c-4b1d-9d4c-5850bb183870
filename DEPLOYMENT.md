# Stack Deployment Guide

This guide will help you deploy your Stack application to production.

## Prerequisites

- Supabase account
- Vercel account (recommended) or any hosting platform that supports Next.js
- Domain name (optional)

## 1. Set Up Supabase for Production

### Create Production Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Choose a strong password for your database
3. Select a region close to your users
4. Wait for the project to be created

### Configure Database

1. Go to the SQL Editor in your Supabase dashboard
2. Copy the entire contents of `schema.sql` from your project
3. Paste and execute the SQL to create all tables, functions, and policies

### Enable Realtime

1. Go to Database > Replication in your Supabase dashboard
2. Enable realtime for these tables:
   - `messages`
   - `user_presence`
   - `typing_indicators`
   - `reactions`
   - `server_members`

### Configure Authentication

1. Go to Authentication > Settings
2. Configure your site URL (e.g., `https://yourdomain.com`)
3. Add redirect URLs for auth callbacks:
   - `https://yourdomain.com/auth/callback`
   - `http://localhost:3000/auth/callback` (for local development)

### Get API Keys

1. Go to Settings > API
2. Copy your project URL and anon key
3. Keep these secure - you'll need them for deployment

## 2. Deploy to Vercel

### Option A: Deploy via GitHub (Recommended)

1. Push your code to a GitHub repository
2. Go to [vercel.com](https://vercel.com) and sign in
3. Click "New Project" and import your GitHub repository
4. Configure environment variables:
   - `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anon key
5. Click "Deploy"

### Option B: Deploy via Vercel CLI

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel

# Set environment variables
vercel env add NEXT_PUBLIC_SUPABASE_URL
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY

# Redeploy with environment variables
vercel --prod
```

## 3. Configure Custom Domain (Optional)

1. In your Vercel dashboard, go to your project settings
2. Click on "Domains"
3. Add your custom domain
4. Configure DNS records as instructed by Vercel
5. Update your Supabase auth settings with the new domain

## 4. Environment Variables

Make sure these environment variables are set in your production environment:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

## 5. Post-Deployment Checklist

### Test Core Functionality

- [ ] User registration and login
- [ ] Server creation
- [ ] Channel creation
- [ ] Real-time messaging
- [ ] User presence updates
- [ ] Typing indicators

### Security Checklist

- [ ] RLS policies are enabled on all tables
- [ ] Environment variables are secure
- [ ] Auth redirect URLs are configured
- [ ] File upload limits are set (if applicable)

### Performance Optimization

- [ ] Enable Vercel Analytics (optional)
- [ ] Configure caching headers
- [ ] Optimize images and assets
- [ ] Monitor database performance

## 6. Monitoring and Maintenance

### Supabase Monitoring

1. Monitor database usage in Supabase dashboard
2. Set up alerts for high usage
3. Review logs for errors

### Vercel Monitoring

1. Monitor function execution times
2. Check for build errors
3. Review analytics data

### Regular Maintenance

- Update dependencies regularly
- Monitor for security vulnerabilities
- Backup your database
- Review and optimize database queries

## 7. Scaling Considerations

### Database Scaling

- Monitor connection limits
- Consider read replicas for high traffic
- Optimize queries and indexes

### Application Scaling

- Use Vercel's edge functions for better performance
- Implement caching strategies
- Consider CDN for static assets

## 8. Troubleshooting

### Common Issues

**Authentication not working:**
- Check redirect URLs in Supabase auth settings
- Verify environment variables are set correctly

**Real-time not working:**
- Ensure realtime is enabled for required tables
- Check RLS policies allow real-time subscriptions

**Database connection errors:**
- Check if you've exceeded connection limits
- Verify database credentials

**Build failures:**
- Check for TypeScript errors
- Verify all dependencies are installed
- Check environment variables are set

### Getting Help

- Check the [Supabase documentation](https://supabase.com/docs)
- Review [Next.js deployment docs](https://nextjs.org/docs/deployment)
- Check [Vercel documentation](https://vercel.com/docs)

## 9. Security Best Practices

- Never expose service role keys in client-side code
- Use RLS policies for all data access
- Validate all user inputs
- Implement rate limiting for API endpoints
- Regular security audits
- Keep dependencies updated

---

Your Stack application should now be successfully deployed and ready for users! 🚀
