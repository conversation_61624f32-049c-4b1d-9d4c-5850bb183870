import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Hash, Users, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface ServerPageProps {
  params: {
    serverId: string
  }
}

export default async function ServerPage({ params }: ServerPageProps) {
  const supabase = await createClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return redirect('/auth/login')
  }

  // Fetch server details
  const { data: server } = await supabase
    .from('servers')
    .select(`
      *,
      server_members!inner(role),
      channels(*)
    `)
    .eq('id', params.serverId)
    .eq('server_members.user_id', user.id)
    .single()

  if (!server) {
    return redirect('/dashboard')
  }

  // Get first text channel and redirect there
  const textChannels = server.channels?.filter(c => c.type === 'text').sort((a, b) => a.position - b.position)
  if (textChannels && textChannels.length > 0) {
    return redirect(`/servers/${params.serverId}/channels/${textChannels[0].id}`)
  }

  const userRole = server.server_members[0]?.role
  const canManage = userRole === 'owner' || userRole === 'admin'

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="max-w-2xl w-full space-y-8">
        {/* Server Header */}
        <div className="text-center space-y-4">
          <div className="w-20 h-20 mx-auto rounded-full gradient-primary flex items-center justify-center">
            {server.icon_url ? (
              <img
                src={server.icon_url}
                alt={server.name}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <span className="text-2xl font-bold text-white">
                {server.name.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          <div>
            <h1 className="text-4xl font-bold">{server.name}</h1>
            {server.description && (
              <p className="text-xl text-muted-foreground mt-2">
                {server.description}
              </p>
            )}
          </div>
        </div>

        {/* Server Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="glass-card">
            <CardContent className="p-6 text-center">
              <Hash className="w-8 h-8 mx-auto mb-2 text-blue-500" />
              <div className="text-2xl font-bold">
                {server.channels?.filter(c => c.type === 'text').length || 0}
              </div>
              <div className="text-sm text-muted-foreground">Text Channels</div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardContent className="p-6 text-center">
              <Users className="w-8 h-8 mx-auto mb-2 text-green-500" />
              <div className="text-2xl font-bold">
                {/* This would be fetched from server_members count */}
                1
              </div>
              <div className="text-sm text-muted-foreground">Members</div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardContent className="p-6 text-center">
              <Settings className="w-8 h-8 mx-auto mb-2 text-purple-500" />
              <div className="text-2xl font-bold">
                {userRole === 'owner' ? 'Owner' : userRole === 'admin' ? 'Admin' : 'Member'}
              </div>
              <div className="text-sm text-muted-foreground">Your Role</div>
            </CardContent>
          </Card>
        </div>

        {/* No Channels Message */}
        {(!server.channels || server.channels.length === 0) && (
          <Card className="glass-card">
            <CardHeader className="text-center">
              <CardTitle>No Channels Yet</CardTitle>
              <CardDescription>
                This server doesn't have any channels yet. 
                {canManage ? ' Create the first channel to get started!' : ' Ask an admin to create some channels.'}
              </CardDescription>
            </CardHeader>
            {canManage && (
              <CardContent className="text-center">
                <Button>
                  Create First Channel
                </Button>
              </CardContent>
            )}
          </Card>
        )}

        {/* Quick Actions */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-center">Quick Actions</h2>
          
          <div className="grid gap-4 md:grid-cols-2">
            {canManage && (
              <Card className="glass-card hover-lift">
                <CardHeader>
                  <CardTitle className="text-lg">Server Settings</CardTitle>
                  <CardDescription>
                    Manage server settings, roles, and permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    Open Settings
                  </Button>
                </CardContent>
              </Card>
            )}

            <Card className="glass-card hover-lift">
              <CardHeader>
                <CardTitle className="text-lg">Invite Members</CardTitle>
                <CardDescription>
                  Share an invite link to grow your community
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Create Invite
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Server Info */}
        <div className="text-center text-sm text-muted-foreground space-y-1">
          <p>Server ID: {server.id}</p>
          <p>Created: {new Date(server.created_at).toLocaleDateString()}</p>
        </div>
      </div>
    </div>
  )
}
