'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Headphones, HeadphonesIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { createClient } from '@/lib/supabase/client'
import { useUserPresenceContext } from '@/components/providers/user-presence-provider'
import type { Profile } from '@/lib/supabase/types'

interface UserProfileProps {
  userId: string
}

export function UserProfile({ userId }: UserProfileProps) {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isMuted, setIsMuted] = useState(false)
  const [isDeafened, setIsDeafened] = useState(false)
  const { getUserPresence, updatePresence } = useUserPresenceContext()
  const supabase = createClient()

  useEffect(() => {
    const fetchProfile = async () => {
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (data) {
        setProfile(data)
      }
    }

    fetchProfile()
  }, [userId, supabase])

  const presence = getUserPresence(userId)
  const status = presence?.status || 'offline'

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500'
      case 'away':
        return 'bg-yellow-500'
      case 'busy':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online'
      case 'away':
        return 'Away'
      case 'busy':
        return 'Do Not Disturb'
      default:
        return 'Offline'
    }
  }

  const toggleMute = () => {
    setIsMuted(!isMuted)
    // In a real app, this would control actual microphone
  }

  const toggleDeafen = () => {
    setIsDeafened(!isDeafened)
    if (!isDeafened) {
      setIsMuted(true) // Deafening also mutes
    }
    // In a real app, this would control actual audio output
  }

  if (!profile) {
    return (
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-full bg-muted animate-pulse" />
          <div className="space-y-1">
            <div className="w-20 h-3 bg-muted rounded animate-pulse" />
            <div className="w-16 h-2 bg-muted rounded animate-pulse" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-between p-3 hover:bg-muted/30 transition-colors">
      {/* User Info */}
      <div className="flex items-center space-x-3 min-w-0 flex-1">
        <div className="relative">
          <Avatar className="w-8 h-8">
            <AvatarImage src={profile.avatar_url || undefined} />
            <AvatarFallback className="text-xs">
              {profile.display_name?.charAt(0) || profile.username.charAt(0)}
            </AvatarFallback>
          </Avatar>
          
          {/* Status indicator */}
          <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${getStatusColor(status)}`} />
        </div>
        
        <div className="min-w-0 flex-1">
          <p className="text-sm font-medium truncate">
            {profile.display_name || profile.username}
          </p>
          <div className="flex items-center space-x-1">
            <Badge 
              variant="secondary" 
              className="text-xs px-1.5 py-0 h-4"
            >
              {getStatusText(status)}
            </Badge>
            {profile.status_text && (
              <span className="text-xs text-muted-foreground truncate">
                {profile.status_text}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center space-x-1">
        <Button
          variant="ghost"
          size="icon"
          className={`w-8 h-8 ${isMuted ? 'text-red-500 hover:text-red-400' : 'hover:text-foreground'}`}
          onClick={toggleMute}
          title={isMuted ? 'Unmute' : 'Mute'}
        >
          {isMuted ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          className={`w-8 h-8 ${isDeafened ? 'text-red-500 hover:text-red-400' : 'hover:text-foreground'}`}
          onClick={toggleDeafen}
          title={isDeafened ? 'Undeafen' : 'Deafen'}
        >
          {isDeafened ? <HeadphonesIcon className="w-4 h-4" /> : <Headphones className="w-4 h-4" />}
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          className="w-8 h-8 hover:text-foreground"
          title="User Settings"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}
