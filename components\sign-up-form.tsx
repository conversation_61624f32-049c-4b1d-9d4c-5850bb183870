"use client";

import { cn } from "@/lib/utils";
import { createClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Info, X } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function SignUpForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [repeatPassword, setRepeatPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [errorDetails, setErrorDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  const router = useRouter();

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    const supabase = createClient();
    setIsLoading(true);
    setError(null);
    setErrorDetails(null);
    setShowErrorDetails(false);

    if (password !== repeatPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    try {
      if (debugMode) console.log("🚀 Starting sign-up process...");

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/protected`,
        },
      });

      if (debugMode) console.log("📊 Sign-up response:", { data, error });

      if (error) {
        console.error("❌ Sign-up error:", error);

        // Capture detailed error information
        const errorInfo = {
          message: error.message,
          status: error.status,
          details: error.__isAuthError ? "Authentication Error" : "Unknown Error",
          timestamp: new Date().toISOString(),
          email: email,
          errorCode: error.status || "UNKNOWN",
        };

        setErrorDetails(errorInfo);

        // Log error to server for debugging
        try {
          await fetch('/api/auth/error-log', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'signup',
              error: errorInfo,
              userAgent: navigator.userAgent,
              url: window.location.href
            })
          });
        } catch (logError) {
          console.warn('Failed to log error to server:', logError);
        }

        // Set user-friendly error message
        let userMessage = error.message;

        if (error.message.includes("Database error saving new user")) {
          userMessage = "Database Error: Failed to create user profile. This is likely a database configuration issue.";
        } else if (error.message.includes("User already registered")) {
          userMessage = "An account with this email already exists. Please try logging in instead.";
        } else if (error.message.includes("Invalid email")) {
          userMessage = "Please enter a valid email address.";
        } else if (error.message.includes("Password")) {
          userMessage = "Password must be at least 6 characters long.";
        }

        setError(userMessage);
        throw error;
      }

      if (data.user) {
        console.log("✅ User created successfully:", data.user.id);
        router.push("/auth/sign-up-success");
      } else {
        console.warn("⚠️ No user data returned");
        setError("Sign-up completed but no user data was returned. Please try logging in.");
      }

    } catch (error: unknown) {
      console.error("💥 Caught error in sign-up:", error);

      if (!errorDetails) {
        // If we haven't already set error details, capture them now
        const errorInfo = {
          message: error instanceof Error ? error.message : "Unknown error occurred",
          type: error instanceof Error ? error.constructor.name : typeof error,
          timestamp: new Date().toISOString(),
          email: email,
          stack: error instanceof Error ? error.stack : undefined,
        };
        setErrorDetails(errorInfo);
      }

      if (!error) {
        setError("An unexpected error occurred during sign-up. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl flex items-center justify-between">
            Sign up
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setDebugMode(!debugMode)}
              className="text-xs opacity-50 hover:opacity-100"
            >
              {debugMode ? "Debug: ON" : "Debug: OFF"}
            </Button>
          </CardTitle>
          <CardDescription>Create a new account</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignUp}>
            <div className="flex flex-col gap-6">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                </div>
                <Input
                  id="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="repeat-password">Repeat Password</Label>
                </div>
                <Input
                  id="repeat-password"
                  type="password"
                  required
                  value={repeatPassword}
                  onChange={(e) => setRepeatPassword(e.target.value)}
                />
              </div>
              {error && (
                <div className="space-y-3">
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription className="font-medium">
                      {error}
                    </AlertDescription>
                  </Alert>

                  {errorDetails && (
                    <div className="space-y-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowErrorDetails(!showErrorDetails)}
                        className="w-full text-xs"
                      >
                        <Info className="h-3 w-3 mr-1" />
                        {showErrorDetails ? "Hide" : "Show"} Technical Details
                      </Button>

                      {showErrorDetails && (
                        <Alert className="bg-muted/50">
                          <AlertDescription>
                            <div className="space-y-2 text-xs font-mono">
                              <div><strong>Error:</strong> {errorDetails.message}</div>
                              <div><strong>Code:</strong> {errorDetails.errorCode || errorDetails.status || "N/A"}</div>
                              <div><strong>Time:</strong> {errorDetails.timestamp}</div>
                              <div><strong>Email:</strong> {errorDetails.email}</div>
                              {errorDetails.type && (
                                <div><strong>Type:</strong> {errorDetails.type}</div>
                              )}
                              {errorDetails.stack && (
                                <details className="mt-2">
                                  <summary className="cursor-pointer text-muted-foreground">Stack Trace</summary>
                                  <pre className="mt-1 text-xs whitespace-pre-wrap break-all">
                                    {errorDetails.stack}
                                  </pre>
                                </details>
                              )}
                            </div>
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}
                </div>
              )}
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Creating an account..." : "Sign up"}
              </Button>
            </div>
            <div className="mt-4 text-center text-sm">
              Already have an account?{" "}
              <Link href="/auth/login" className="underline underline-offset-4">
                Login
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
