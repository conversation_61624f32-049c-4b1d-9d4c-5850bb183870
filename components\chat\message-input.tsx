'use client'

import { useState, useRef, useCallback } from 'react'
import { Send, Paperclip, Smile, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'

interface MessageInputProps {
  onSendMessage: (content: string, replyTo?: string) => Promise<void>
  onStartTyping: () => void
  onStopTyping: () => void
  placeholder?: string
  replyTo?: string
  onCancelReply?: () => void
}

export function MessageInput({
  onSendMessage,
  onStartTyping,
  onStopTyping,
  placeholder = "Type a message...",
  replyTo,
  onCancelReply
}: MessageInputProps) {
  const [message, setMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    setMessage(value)

    // Handle typing indicators
    if (value.length > 0 && !isTyping) {
      setIsTyping(true)
      onStartTyping()
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
      onStopTyping()
    }, 3000)

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [isTyping, onStartTyping, onStopTyping])

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }, [message])

  const handleSend = useCallback(async () => {
    const trimmedMessage = message.trim()
    if (!trimmedMessage || isSending) return

    setIsSending(true)

    try {
      await onSendMessage(trimmedMessage, replyTo)
      setMessage('')
      
      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false)
        onStopTyping()
      }

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'
      }

      // Cancel reply if exists
      if (replyTo && onCancelReply) {
        onCancelReply()
      }
    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      setIsSending(false)
    }
  }, [message, isSending, onSendMessage, replyTo, isTyping, onStopTyping, onCancelReply])

  const handleBlur = useCallback(() => {
    // Stop typing when input loses focus
    if (isTyping) {
      setIsTyping(false)
      onStopTyping()
    }
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }
  }, [isTyping, onStopTyping])

  return (
    <div className="p-4">
      {/* Reply indicator */}
      {replyTo && (
        <div className="mb-2 p-2 bg-muted/50 rounded-lg flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            Replying to message
          </span>
          {onCancelReply && (
            <Button variant="ghost" size="sm" onClick={onCancelReply}>
              Cancel
            </Button>
          )}
        </div>
      )}

      <div className="flex items-end space-x-2">
        {/* Add attachment button */}
        <Button
          variant="ghost"
          size="icon"
          className="w-10 h-10 rounded-full hover:bg-primary/20"
          disabled={isSending}
        >
          <Plus className="w-5 h-5" />
        </Button>

        {/* Message input container */}
        <div className="flex-1 relative">
          <div className="glass-card border border-border/50 rounded-lg overflow-hidden">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              placeholder={placeholder}
              className={cn(
                "min-h-[44px] max-h-[120px] resize-none border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0",
                "placeholder:text-muted-foreground"
              )}
              disabled={isSending}
              rows={1}
            />
            
            {/* Input actions */}
            <div className="flex items-center justify-between p-2 border-t border-border/50">
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  disabled={isSending}
                >
                  <Paperclip className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  disabled={isSending}
                >
                  <Smile className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-xs text-muted-foreground">
                  {message.length}/2000
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Send button */}
        <Button
          onClick={handleSend}
          disabled={!message.trim() || isSending}
          size="icon"
          className="w-10 h-10 rounded-full gradient-primary"
        >
          <Send className="w-5 h-5" />
        </Button>
      </div>

      {/* Character limit warning */}
      {message.length > 1800 && (
        <div className="mt-2 text-xs text-yellow-500">
          {2000 - message.length} characters remaining
        </div>
      )}
    </div>
  )
}
