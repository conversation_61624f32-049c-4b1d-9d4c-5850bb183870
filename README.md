# Stack - Modern Discord-like Communication Platform

Stack is a comprehensive, modern Discord-like communication platform built with Next.js 15, React 19, Supabase, and Tailwind CSS. It features real-time messaging, server management, beautiful glassmorphism UI, and all the modern features you'd expect from a communication platform.

<p align="center">
  <a href="#features"><strong>Features</strong></a> ·
  <a href="#tech-stack"><strong>Tech Stack</strong></a> ·
  <a href="#getting-started"><strong>Getting Started</strong></a> ·
  <a href="#deployment"><strong>Deployment</strong></a> ·
  <a href="#contributing"><strong>Contributing</strong></a>
</p>
<br/>

## ✨ Features

### 🚀 Core Features
- **Real-time Messaging** - Instant message delivery with Supabase Realtime
- **Server Management** - Create and manage multiple servers (guilds)
- **Channel System** - Text and voice channels with categories
- **User Presence** - Online/away/busy/offline status tracking
- **Typing Indicators** - See when users are typing
- **Message Reactions** - React to messages with emojis
- **File Attachments** - Share files and images
- **Message Replies** - Reply to specific messages
- **User Profiles** - Customizable user profiles with avatars

### 🎨 Modern UI/UX
- **Glassmorphism Design** - Beautiful glass-like effects with backdrop blur
- **Subtle Gradients** - Modern gradient backgrounds and elements
- **Dark/Light Theme** - Automatic theme switching support
- **Responsive Design** - Works perfectly on desktop and mobile
- **Smooth Animations** - Micro-interactions and transitions
- **Accessibility** - Built with accessibility in mind

### 🔧 Technical Features
- **Real-time Subscriptions** - Powered by Supabase Realtime
- **Optimistic Updates** - Instant UI updates with server sync
- **Row Level Security** - Comprehensive database security
- **TypeScript** - Fully typed for better development experience
- **Server-Side Rendering** - Fast initial page loads
- **Modern React** - Built with React 19 and Next.js 15

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: Supabase (Database, Auth, Realtime, Storage)
- **Styling**: Tailwind CSS v3 with custom glassmorphism effects
- **UI Components**: Radix UI primitives with shadcn/ui
- **Real-time**: Supabase Realtime subscriptions
- **State Management**: React hooks with optimistic updates
- **Authentication**: Supabase Auth
- **Database**: PostgreSQL with Row Level Security

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase account

### 1. Clone the Repository

```bash
git clone <repository-url>
cd stack-chat
npm install
```

### 2. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Copy `.env.local.example` to `.env.local` and fill in your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Set Up the Database

Run the SQL schema in your Supabase SQL editor:

```bash
# Copy the contents of schema.sql and run it in Supabase SQL Editor
```

This will create all necessary tables, functions, triggers, and RLS policies.

### 4. Enable Realtime

In your Supabase dashboard:
1. Go to Database > Replication
2. Enable realtime for these tables:
   - `messages`
   - `user_presence`
   - `typing_indicators`
   - `reactions`
   - `server_members`

### 5. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
app/
├── (auth)/                 # Authentication pages
├── (dashboard)/           # Main application
│   ├── layout.tsx        # Dashboard layout with sidebar
│   ├── page.tsx          # Dashboard home
│   └── servers/          # Server-specific pages
components/
├── auth/                 # Authentication components
├── chat/                 # Chat-related components
├── sidebar/              # Navigation components
├── modals/               # Modal dialogs
├── ui/                   # Base UI components
└── providers/            # Context providers
lib/
├── supabase/             # Supabase utilities and types
├── hooks/                # Custom React hooks
└── utils/                # Utility functions
```

## 🗄️ Database Schema

The application uses a comprehensive PostgreSQL schema with the following main tables:

- **profiles** - User profile information
- **servers** - Server/guild data
- **channels** - Channel information
- **messages** - Chat messages with real-time updates
- **server_members** - Server membership and roles
- **attachments** - File uploads and media
- **reactions** - Message reactions
- **user_presence** - Real-time user status
- **typing_indicators** - Typing status

All tables include proper Row Level Security (RLS) policies for data protection.

## 🔐 Security

- **Row Level Security** - All database tables protected with RLS
- **Authentication** - Secure user authentication via Supabase Auth
- **Input Validation** - Comprehensive input validation and sanitization
- **File Upload Security** - Secure file handling and validation
- **Rate Limiting** - Protection against spam and abuse

## 🚀 Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add your environment variables in Vercel dashboard
4. Deploy!

### Environment Variables for Production

```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Powered by [Supabase](https://supabase.com/)
- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Icons from [Lucide](https://lucide.dev/)

---

**Stack** - Modern communication, beautifully crafted. 💬✨
