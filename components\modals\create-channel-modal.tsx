'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Hash, Volume2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { createClient } from '@/lib/supabase/client'
import { toast } from 'sonner'

interface CreateChannelModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  serverId: string
}

export function CreateChannelModal({ open, onOpenChange, serverId }: CreateChannelModalProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [type, setType] = useState<'text' | 'voice'>('text')
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      toast.error('Channel name is required')
      return
    }

    // Validate channel name (Discord-like rules)
    const channelName = name.trim().toLowerCase().replace(/[^a-z0-9-_]/g, '-')
    if (channelName !== name.trim().toLowerCase()) {
      setName(channelName)
      toast.info('Channel name has been formatted to follow naming rules')
      return
    }

    setLoading(true)

    try {
      // Get the highest position for ordering
      const { data: existingChannels } = await supabase
        .from('channels')
        .select('position')
        .eq('server_id', serverId)
        .eq('type', type)
        .order('position', { ascending: false })
        .limit(1)

      const nextPosition = existingChannels?.[0]?.position ? existingChannels[0].position + 1 : 0

      // Create channel
      const { data: channel, error } = await supabase
        .from('channels')
        .insert({
          server_id: serverId,
          name: channelName,
          description: description.trim() || null,
          type,
          position: nextPosition
        })
        .select()
        .single()

      if (error) throw error

      toast.success(`${type === 'text' ? 'Text' : 'Voice'} channel created successfully!`)
      
      // Reset form
      setName('')
      setDescription('')
      setType('text')
      onOpenChange(false)
      
      // Navigate to new channel if it's a text channel
      if (type === 'text') {
        router.push(`/servers/${serverId}/channels/${channel.id}`)
      }
      
      router.refresh()
      
    } catch (error) {
      console.error('Error creating channel:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create channel')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="glass-card border-border/50">
        <DialogHeader>
          <DialogTitle>Create Channel</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-3">
            <Label>Channel Type</Label>
            <RadioGroup value={type} onValueChange={(value) => setType(value as 'text' | 'voice')}>
              <div className="flex items-center space-x-2 p-3 rounded-lg border border-border/50 hover:bg-muted/30 transition-colors">
                <RadioGroupItem value="text" id="text" />
                <Hash className="w-5 h-5 text-muted-foreground" />
                <div className="flex-1">
                  <Label htmlFor="text" className="font-medium">Text Channel</Label>
                  <p className="text-sm text-muted-foreground">Send messages, images, GIFs, emoji, opinions, and puns</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 p-3 rounded-lg border border-border/50 hover:bg-muted/30 transition-colors">
                <RadioGroupItem value="voice" id="voice" />
                <Volume2 className="w-5 h-5 text-muted-foreground" />
                <div className="flex-1">
                  <Label htmlFor="voice" className="font-medium">Voice Channel</Label>
                  <p className="text-sm text-muted-foreground">Hang out together with voice, video, and screen share</p>
                </div>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="channel-name">Channel Name</Label>
            <Input
              id="channel-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder={type === 'text' ? 'new-channel' : 'General'}
              maxLength={100}
              required
            />
            <p className="text-xs text-muted-foreground">
              {type === 'text' && 'Use lowercase letters, numbers, and dashes'}
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="channel-description">Description (Optional)</Label>
            <Textarea
              id="channel-description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="What's this channel about?"
              maxLength={500}
              rows={3}
            />
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !name.trim()}>
              {loading ? 'Creating...' : 'Create Channel'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
