'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Plus, Hash, Volume2, Settings, Users, Crown, Shield, Mic } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { UserProfile } from './user-profile'
import { CreateServerModal } from '@/components/modals/create-server-modal'
import { CreateChannelModal } from '@/components/modals/create-channel-modal'
import type { ServerWithChannels } from '@/lib/supabase/types'

interface SidebarProps {
  servers: ServerWithChannels[]
  currentUserId: string
}

export function Sidebar({ servers, currentUserId }: SidebarProps) {
  const pathname = usePathname()
  const [selectedServerId, setSelectedServerId] = useState<string | null>(null)
  const [showCreateServer, setShowCreateServer] = useState(false)
  const [showCreateChannel, setShowCreate<PERSON>hannel] = useState(false)

  // Get current server from URL
  const currentServerId = pathname.split('/')[2]
  const currentServer = servers.find(s => s.id === currentServerId)

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="w-3 h-3 text-yellow-500" />
      case 'admin':
        return <Shield className="w-3 h-3 text-red-500" />
      case 'moderator':
        return <Mic className="w-3 h-3 text-blue-500" />
      default:
        return null
    }
  }

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'voice':
        return <Volume2 className="w-4 h-4" />
      case 'category':
        return null
      default:
        return <Hash className="w-4 h-4" />
    }
  }

  return (
    <div className="flex h-full">
      {/* Server List */}
      <div className="w-16 bg-background/50 backdrop-blur-sm border-r border-border/50 flex flex-col items-center py-3 space-y-2">
        <ScrollArea className="flex-1 w-full">
          <div className="space-y-2 px-2">
            {servers.map((server) => (
              <Link
                key={server.id}
                href={`/servers/${server.id}`}
                className={cn(
                  "w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-200 hover:rounded-xl group relative",
                  currentServerId === server.id
                    ? "bg-primary text-primary-foreground rounded-xl"
                    : "bg-muted hover:bg-primary/20 hover:text-primary"
                )}
                onClick={() => setSelectedServerId(server.id)}
              >
                {server.icon_url ? (
                  <img
                    src={server.icon_url}
                    alt={server.name}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <span className="font-semibold text-sm">
                    {server.name.charAt(0).toUpperCase()}
                  </span>
                )}
                
                {/* Server name tooltip */}
                <div className="absolute left-16 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {server.name}
                </div>
              </Link>
            ))}
            
            {/* Add Server Button */}
            <Button
              variant="ghost"
              size="icon"
              className="w-12 h-12 rounded-2xl hover:rounded-xl hover:bg-primary/20 hover:text-primary transition-all duration-200 group"
              onClick={() => setShowCreateServer(true)}
            >
              <Plus className="w-6 h-6" />
              <div className="absolute left-16 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                Add Server
              </div>
            </Button>
          </div>
        </ScrollArea>
      </div>

      {/* Server Content */}
      <div className="flex-1 flex flex-col">
        {currentServer ? (
          <>
            {/* Server Header */}
            <div className="h-12 border-b border-border/50 flex items-center justify-between px-4 bg-background/80 backdrop-blur-sm">
              <h2 className="font-semibold text-sm truncate">{currentServer.name}</h2>
              <Button variant="ghost" size="icon" className="w-6 h-6">
                <Settings className="w-4 h-4" />
              </Button>
            </div>

            {/* Channels */}
            <ScrollArea className="flex-1 custom-scrollbar">
              <div className="p-2 space-y-1">
                {/* Text Channels Section */}
                <div className="flex items-center justify-between px-2 py-1">
                  <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                    Text Channels
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-4 h-4 opacity-60 hover:opacity-100"
                    onClick={() => setShowCreateChannel(true)}
                  >
                    <Plus className="w-3 h-3" />
                  </Button>
                </div>

                {currentServer.channels
                  ?.filter(channel => channel.type === 'text')
                  .sort((a, b) => a.position - b.position)
                  .map((channel) => (
                    <Link
                      key={channel.id}
                      href={`/servers/${currentServer.id}/channels/${channel.id}`}
                      className={cn(
                        "flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-muted/50 transition-colors group",
                        pathname.includes(channel.id) && "bg-muted text-foreground"
                      )}
                    >
                      {getChannelIcon(channel.type)}
                      <span className="text-sm truncate">{channel.name}</span>
                    </Link>
                  ))}

                {/* Voice Channels Section */}
                {currentServer.channels?.some(c => c.type === 'voice') && (
                  <>
                    <div className="flex items-center justify-between px-2 py-1 mt-4">
                      <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                        Voice Channels
                      </span>
                    </div>

                    {currentServer.channels
                      ?.filter(channel => channel.type === 'voice')
                      .sort((a, b) => a.position - b.position)
                      .map((channel) => (
                        <div
                          key={channel.id}
                          className="flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-muted/50 transition-colors group"
                        >
                          {getChannelIcon(channel.type)}
                          <span className="text-sm truncate">{channel.name}</span>
                        </div>
                      ))}
                  </>
                )}

                {/* Members Section */}
                <div className="flex items-center justify-between px-2 py-1 mt-4">
                  <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                    Members
                  </span>
                  <Users className="w-3 h-3 text-muted-foreground" />
                </div>

                {/* Server members would go here */}
                <div className="space-y-1">
                  {/* Placeholder for members list */}
                </div>
              </div>
            </ScrollArea>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">Select a server to get started</p>
            </div>
          </div>
        )}

        {/* User Profile */}
        <div className="h-16 border-t border-border/50 bg-background/80 backdrop-blur-sm">
          <UserProfile userId={currentUserId} />
        </div>
      </div>

      {/* Modals */}
      <CreateServerModal
        open={showCreateServer}
        onOpenChange={setShowCreateServer}
      />
      
      {currentServer && (
        <CreateChannelModal
          open={showCreateChannel}
          onOpenChange={setShowCreateChannel}
          serverId={currentServer.id}
        />
      )}
    </div>
  )
}
